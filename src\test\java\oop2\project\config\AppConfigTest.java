package oop2.project.config;

import oop2.project.model.Language;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for AppConfig comprehensive coverage.
 */
public class AppConfigTest {

    private AppConfig config;

    @BeforeEach
    public void setUp() {
        config = AppConfig.getInstance();
    }

    @Test
    public void testLanguageHandling() {
        // When setting a language
        config.setLanguage(Language.English);

        // Then it should be retrievable correctly
        Language retrievedLanguage = config.getLanguage();
        assertEquals(Language.English, retrievedLanguage);
        assertEquals("en-US", retrievedLanguage.getLanguageCode());
    }

    @Test
    public void testLanguageHandlingWithGerman() {
        // When setting German language
        config.setLanguage(Language.Deutsch);

        // Then it should be retrievable correctly
        Language retrievedLanguage = config.getLanguage();
        assertEquals(Language.Deutsch, retrievedLanguage);
        assertEquals("de-CH", retrievedLanguage.getLanguageCode());
    }

    @Test
    public void testDefaultLanguageHandling() {
        // When getting the default language
        Language defaultLanguage = config.getLanguage();

        // Then it should be English (default)
        assertNotNull(defaultLanguage);
        assertEquals("en-US", defaultLanguage.getLanguageCode());
    }

    @Test
    public void testMinimumRatingHandling() {
        // When setting minimum rating
        config.setMinimumRating(7.5);

        // Then it should be retrievable correctly
        double retrievedRating = config.getMinimumRating();
        assertEquals(7.5, retrievedRating, 0.01);
    }

    @Test
    public void testAdultContentHandling() {
        // When setting adult content preference
        config.setAllowAdultContent(true);

        // Then it should be retrievable correctly
        assertTrue(config.allowAdultContent());

        // When setting to false
        config.setAllowAdultContent(false);

        // Then it should be false
        assertFalse(config.allowAdultContent());
    }

    @Test
    public void testApiKeyHandling() {
        // When setting OpenAI API key
        config.setOpenAiApiKey("test-openai-key");

        // Then it should be retrievable correctly
        assertEquals("test-openai-key", config.getOpenAiApiKey());

        // When setting TMDB API key
        config.setTmdbApiKey("test-tmdb-key");

        // Then it should be retrievable correctly
        assertEquals("test-tmdb-key", config.getTmdbApiKey());
    }

    @Test
    public void testIsValidWithBothKeys() {
        // When both keys are set
        config.setOpenAiApiKey("test-openai-key");
        config.setTmdbApiKey("test-tmdb-key");

        // Then config should be valid
        assertTrue(config.isValid());
    }

    @Test
    public void testIsValidWithMissingKeys() {
        // When only one key is set
        config.setOpenAiApiKey("test-openai-key");
        config.setTmdbApiKey(""); // Empty key

        // Then config should be invalid
        assertFalse(config.isValid());

        // When no keys are set
        config.setOpenAiApiKey("");
        config.setTmdbApiKey("");

        // Then config should be invalid
        assertFalse(config.isValid());
    }

    @Test
    public void testGenericSetMethod() {
        // When setting a custom property
        config.set("customKey", "customValue");

        // Then it should be retrievable
        assertEquals("customValue", config.get("customKey"));
    }

    @Test
    public void testGenericGetWithDifferentTypes() {
        // Test String type
        config.set("stringKey", "testValue");
        assertEquals("testValue", config.get("stringKey", String.class));

        // Test Boolean type
        config.set("booleanKey", "true");
        assertEquals(Boolean.TRUE, config.get("booleanKey", Boolean.class));

        // Test Integer type
        config.set("integerKey", "42");
        assertEquals(Integer.valueOf(42), config.get("integerKey", Integer.class));

        // Test Float type
        config.set("floatKey", "3.14");
        assertEquals(Float.valueOf(3.14f), config.get("floatKey", Float.class));

        // Test Double type
        config.set("doubleKey", "2.718");
        assertEquals(Double.valueOf(2.718), config.get("doubleKey", Double.class));
    }

    @Test
    public void testGenericGetWithNullValue() {
        // When getting a non-existent key
        String result = config.get("nonExistentKey", String.class);

        // Then it should return null
        assertNull(result);
    }

    @Test
    public void testGenericGetWithUnsupportedType() {
        config.set("testKey", "testValue");

        // When getting with unsupported type
        Object result = config.get("testKey", Object.class);

        // Then it should return null
        assertNull(result);
    }

    @Test
    public void testGenericGetWithInvalidValue() {
        config.set("invalidIntKey", "notAnInteger");

        // When getting as Integer
        Integer result = config.get("invalidIntKey", Integer.class);

        // Then it should return null due to parsing error
        assertNull(result);
    }

    @Test
    public void testSingletonPattern() {
        AppConfig config1 = AppConfig.getInstance();
        AppConfig config2 = AppConfig.getInstance();

        // Then both should be the same instance
        assertSame(config1, config2);
    }

    @Test
    public void testConstants() {
        // Test that all constants are properly defined
        assertEquals("en-US", AppConfig.DEFAULT_LANGUAGE);
        assertFalse(AppConfig.DEFAULT_ALLOW_ADULT_CONTENT);
        assertEquals(-1.0, AppConfig.DEFAULT_MINIMUM_RATING, 0.01);

        assertEquals("language", AppConfig.KEY_LANGUAGE);
        assertEquals("allowAdultContent", AppConfig.KEY_ALLOW_ADULT_CONTENT);
        assertEquals("minimumRating", AppConfig.KEY_MINIMUM_RATING);
        assertEquals("openAiApiKey", AppConfig.KEY_OPENAI_API_KEY);
        assertEquals("tmdbApiKey", AppConfig.KEY_TMDB_API_KEY);
    }

    @Test
    public void testSaveWhenNotDirty() throws Exception {
        // Use reflection to set isDirty to false
        java.lang.reflect.Field isDirtyField = AppConfig.class.getDeclaredField("isDirty");
        isDirtyField.setAccessible(true);
        isDirtyField.set(config, false);

        // When calling save
        config.save();

        // Then save should return early (this tests the early return path)
        assertFalse(isDirtyField.getBoolean(config));
    }

    @Test
    public void testInvalidLanguageCodeHandling() {
        // Set an invalid language code directly in properties
        config.set(AppConfig.KEY_LANGUAGE, "invalid-language-code");

        // When getting the language
        Language language = config.getLanguage();

        // Then it should fall back to default language
        assertEquals(Language.English, language);
        assertEquals(AppConfig.DEFAULT_LANGUAGE, language.getLanguageCode());
    }

    @Test
    public void testSaveWhenDirty() throws Exception {
        // Make the config dirty by setting a value
        config.set("testKey", "testValue");

        // Use reflection to check if isDirty is true
        java.lang.reflect.Field isDirtyField = AppConfig.class.getDeclaredField("isDirty");
        isDirtyField.setAccessible(true);
        assertTrue(isDirtyField.getBoolean(config));

        // When calling save (this will attempt to save but may fail due to file permissions)
        // We're mainly testing that the method doesn't throw an exception
        assertDoesNotThrow(() -> config.save());
    }
}
