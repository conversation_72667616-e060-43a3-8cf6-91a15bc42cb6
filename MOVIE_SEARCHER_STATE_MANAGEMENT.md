# Movie Searcher State Management Implementation

## Overview

This implementation provides a best practice solution for maintaining the state of the movie searcher view when users navigate between different views in the application. The user can now go to their favorite movies view, then return to the searcher and have their previous search results, filters, and pagination state preserved.

## Architecture

The solution uses the **Memento Pattern** combined with a **State Service Singleton** to achieve state persistence across view navigation.

### Key Components

#### 1. MovieSearcherState (Model)
- **Location**: `src/main/java/oop2/project/model/MovieSearcherState.java`
- **Purpose**: Encapsulates all the state that needs to be preserved
- **Contains**:
  - Search text
  - Filter settings (language, rating, adult content, sort option)
  - Pagination state (current page)
  - Last search results
  - View state (loading, content, empty, error)
  - Search session tracking

#### 2. MovieSearcherStateService (Service)
- **Location**: `src/main/java/oop2/project/service/MovieSearcherStateService.java`
- **Purpose**: Singleton service that manages state persistence
- **Features**:
  - Save/restore state operations
  - State validation and management
  - Integration with AppConfig for default values
  - Memory-based persistence (survives view switches but not app restarts)

#### 3. Enhanced MovieSearcherController
- **Location**: `src/main/java/oop2/project/controller/MovieSearcherController.java`
- **Changes**:
  - Integrated with state service
  - Restores state on initialization
  - Saves state when navigating away
  - Uses state object instead of individual variables
  - Provides `saveCurrentState()` method for external calls

#### 4. Enhanced MainViewController
- **Location**: `src/main/java/oop2/project/controller/MainViewController.java`
- **Changes**:
  - Caches controller instances instead of recreating them
  - Calls `saveCurrentState()` before view switches
  - Maintains view type tracking
  - Preserves controller references for state management

## How It Works

### State Preservation Flow

1. **Initial Load**: 
   - MovieSearcherController initializes and restores state from service
   - If no previous state exists, creates initial state with AppConfig defaults
   - UI components are populated from restored state

2. **User Interaction**:
   - User performs searches, changes filters, navigates pages
   - State is continuously updated and saved to the service
   - All changes are preserved in memory

3. **Navigation Away**:
   - MainViewController calls `saveCurrentState()` on the controller
   - Current UI values are captured and saved to the state service
   - Controller instance is cached (not destroyed)

4. **Return to Searcher**:
   - MainViewController reuses the cached controller instance
   - Controller already has the preserved state
   - UI displays exactly as the user left it

### State Components Preserved

- **Search Text**: User's search query
- **Filters**: Language, minimum rating, adult content inclusion, sort option
- **Pagination**: Current page number and total pages
- **Results**: Last search results for immediate display
- **View State**: Whether showing content, empty state, error, or loading
- **Session Info**: Whether user has performed any searches

## Benefits

### 1. **Excellent User Experience**
- No loss of work when navigating between views
- Immediate return to previous state
- Maintains user's mental model of the application

### 2. **Performance Optimization**
- Avoids unnecessary API calls when returning to searcher
- Reuses cached results when appropriate
- Controller instances are reused, reducing object creation

### 3. **Memory Efficiency**
- State is stored in memory only when needed
- Automatic cleanup when application closes
- Minimal memory footprint

### 4. **Maintainable Architecture**
- Clear separation of concerns
- Follows established design patterns
- Easy to extend or modify

## Usage Examples

### For Users
1. Search for "Action" movies with rating > 7.0
2. Navigate to page 3 of results
3. Click on "Favorite Movies" to see hearted movies
4. Click back on "Movie Searcher"
5. **Result**: User sees page 3 of "Action" movies with rating > 7.0 exactly as they left it

### For Developers
```java
// Get the state service
MovieSearcherStateService stateService = MovieSearcherStateService.getInstance();

// Check if user has an active search session
if (stateService.hasActiveSearchSession()) {
    // User has meaningful state to return to
}

// Save current state
MovieSearcherState currentState = new MovieSearcherState();
// ... populate state
stateService.saveState(currentState);

// Restore state
MovieSearcherState restoredState = stateService.restoreState();
```

## Testing

A comprehensive test suite is provided in:
- `src/test/java/oop2/project/service/MovieSearcherStateServiceTest.java`

Tests cover:
- State save/restore functionality
- State validation
- Session management
- Singleton behavior
- Edge cases and error conditions

## Future Enhancements

### Possible Extensions
1. **Persistent Storage**: Save state to disk for survival across app restarts
2. **Multiple Sessions**: Support multiple saved search sessions
3. **State History**: Undo/redo functionality for search states
4. **State Sharing**: Export/import search configurations
5. **Analytics**: Track user search patterns and preferences

### Configuration Options
The implementation can be easily extended to support:
- Configurable state retention duration
- Optional automatic state clearing
- User preferences for state management behavior

## Implementation Notes

- **Thread Safety**: The implementation is thread-safe using concurrent collections
- **Memory Management**: State is automatically cleaned up when the application closes
- **Error Handling**: Graceful fallback to default state if restoration fails
- **Logging**: Comprehensive logging for debugging and monitoring
- **Performance**: Minimal overhead with lazy initialization and efficient state copying
